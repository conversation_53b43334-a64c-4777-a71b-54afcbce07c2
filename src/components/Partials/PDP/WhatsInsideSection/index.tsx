"use client";

import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import { getStrapiUrl } from "@/utils/strapiUrl";
import { UnderLine } from "@/assets/icons/UnderLine";
import { WhatsInsideType } from "@/types/PDP/WhatInside";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from "@/components/ui/carousel";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/libs/utils";

interface WhatsInsideSectionProps {
  data?: WhatsInsideType;
  primaryColor?: string;
  productType?: "BYOB" | "COMBO" | "VARIANT";
  bundleVariants?: any[]; // Array of bundle variants for BYOB and COMBO products
  onElementMount?: (element: HTMLElement | null) => void; // Callback for element mounting
}

const WhatsInsideSection: React.FC<WhatsInsideSectionProps> = ({
  data,
  primaryColor = "#ffffff", // Default theme color
  productType = "VARIANT",
  bundleVariants = [],
  onElementMount,
}) => {
  const [carouselApi, setCarouselApi] = useState<CarouselApi>();
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);

  // Register element with parent component for visibility tracking
  useEffect(() => {
    if (onElementMount && sectionRef.current) {
      onElementMount(sectionRef.current);
    }
    return () => {
      if (onElementMount) {
        onElementMount(null);
      }
    };
  }, [onElementMount]);

  // Update navigation button states
  useEffect(() => {
    if (!carouselApi) return;

    const updateButtonStates = () => {
      setCanScrollPrev(carouselApi.canScrollPrev());
      setCanScrollNext(carouselApi.canScrollNext());
    };

    // Initial state
    updateButtonStates();

    // Listen for carousel changes
    carouselApi.on("select", updateButtonStates);
    carouselApi.on("reInit", updateButtonStates);

    return () => {
      carouselApi.off("select", updateButtonStates);
      carouselApi.off("reInit", updateButtonStates);
    };
  }, [carouselApi]);

  // Determine data source based on product type
  let whatsInsideData;
  let shouldShow = false;

  // Check if product type is BYOB or COMBO (both use the same logic)
  const isBundleProduct = productType === "BYOB" || productType === "COMBO";

  if (isBundleProduct) {
    // For BYOB and COMBO products, use bundle variants' whats_inside data
    const validBundleVariants = bundleVariants.filter(
      (variant) => variant.whats_inside && variant.whats_inside.show_component
    );

    if (validBundleVariants.length === 1) {
      // Single bundle variant: use its whats_inside data
      whatsInsideData = validBundleVariants[0].whats_inside;
      shouldShow = true;
    } else if (validBundleVariants.length > 1) {
      // Multiple bundle variants: create carousel data
      whatsInsideData = {
        title: "What's Inside",
        show_component: true,
        whats_inside_details: validBundleVariants
          .map((variant) => ({
            ...variant.whats_inside.whats_inside_details[0], // Take first detail from each variant
            title: variant.title, // Use variant title
          }))
          .filter(Boolean),
      };
      shouldShow = true;
    } else {
      console.log("❌ No valid bundle variants found for WhatsInside");
    }
  } else {
    // For VARIANT products, use regular data
    whatsInsideData = data;
    shouldShow = !!(data && data.show_component);
  }

  // Early return if no data or component should not be shown
  if (!shouldShow || !whatsInsideData) {
    return null;
  }

  const hasMultipleItems = whatsInsideData.whats_inside_details.length > 1;

  // Helper function to render individual item content
  const renderItemContent = (
    detail: (typeof whatsInsideData.whats_inside_details)[0],
    index: number
  ) => (
    <div key={index} className="flex flex-col">
      <div
        className={cn(
          "relative w-full flex items-center justify-center",
          hasMultipleItems ? "h-[285px]" : "h-[325px]"
        )}
      >
        <div
          className={cn(
            "relative",
            hasMultipleItems ? "w-[285px] h-[285px]" : "w-[325px] h-[325px]"
          )}
        >
          <Image
            src={getStrapiUrl(detail.image?.web?.url) || ""}
            alt={detail.image?.web?.alternativeText || ""}
            fill
            style={{ objectFit: "contain" }}
            className="object-contain"
          />
        </div>
      </div>
      <div id="ingredients-section">
        <h2
          className={cn(
            "font-narrow font-semibold mb-6 leading-8 text-[#1a181e]",
            hasMultipleItems ? "text-xl" : "text-2xl"
          )}
        >
          {detail.title}
        </h2>
        <ul className="list-none">
          {detail.details.map((item, itemIndex) => (
            <li
              key={itemIndex}
              className={cn(
                "leading-7 font-normal text-[#1a181e] font-obviously flex items-start",
                hasMultipleItems ? "text-base" : "text-lg"
              )}
            >
              <div className="flex-shrink-0">{item.key}</div>
              <div className="flex-1 border-t-2 border-dotted border-[#00000080] mx-2 mt-[18px]"></div>
              <div className="flex-shrink-0">{item.value}%</div>
            </li>
          ))}
          <li
            className={cn(
              "text-black pt-2.5 text-2xl font-normal leading-8 font-gooddog flex items-end justify-end flex-col",
              hasMultipleItems && "text-base lg:text-xl"
            )}
          >
            <UnderLine />
            100%
          </li>
        </ul>
      </div>
    </div>
  );

  return (
    <div
      id="whats-inside-section"
      ref={sectionRef}
      className="mt-4 flex flex-col justify-center w-full"
    >
      <h1
        className={cn(
          "font-narrow font-semibold mb-4 text-4xl leading-8 text-[#1a181e]"
        )}
      >
        {whatsInsideData.title}
      </h1>

      {hasMultipleItems ? (
        // Carousel mode for multiple items
        <div className="relative">
          <Carousel
            className="w-full relative"
            opts={{
              loop: false,
              align: "start",
              slidesToScroll: 1,
            }}
            setApi={setCarouselApi}
          >
            <CarouselContent className="-ml-4">
              {whatsInsideData.whats_inside_details.map((detail, index) => (
                <CarouselItem key={index} className="pl-4 md:basis-1/2">
                  {renderItemContent(detail, index)}
                </CarouselItem>
              ))}
            </CarouselContent>

            {/* Navigation buttons - conditionally rendered */}
            {canScrollPrev && (
              <CarouselPrevious
                className="shadow-none block absolute -left-4 md:-left-12 top-1/2 -translate-y-1/2 bg-transparent hover:bg-transparent border-none !h-8 !w-8 cursor-pointer"
                icon={
                  <ChevronLeft
                    style={{
                      color: primaryColor,
                      width: "32px",
                      height: "32px",
                    }}
                  />
                }
              />
            )}
            {canScrollNext && (
              <CarouselNext
                className="shadow-none block absolute -right-4 md:-right-12 top-1/2 -translate-y-1/2 bg-transparent hover:bg-transparent border-none !h-8 !w-8 cursor-pointer"
                icon={
                  <ChevronRight
                    style={{
                      color: primaryColor,
                      width: "32px",
                      height: "32px",
                    }}
                  />
                }
              />
            )}
          </Carousel>
        </div>
      ) : (
        // Single item mode - no carousel
        whatsInsideData.whats_inside_details.map((detail, index) => (
          <React.Fragment key={index}>
            {renderItemContent(detail, index)}
          </React.Fragment>
        ))
      )}
    </div>
  );
};

export default WhatsInsideSection;
